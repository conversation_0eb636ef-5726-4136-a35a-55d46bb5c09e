#!/bin/bash

# WordPress Docker镜像切换脚本

echo "🐳 WordPress Docker镜像切换工具"
echo "================================"

# 显示当前配置
show_current_config() {
    if [ -f .env ]; then
        current_image=$(grep "WP_IMAGE=" .env | cut -d'=' -f2)
        echo "当前镜像: ${current_image:-wordpress:latest}"
    else
        echo "当前镜像: wordpress:latest (默认)"
    fi
}

# 显示菜单
show_menu() {
    echo ""
    echo "可选的WordPress镜像配置："
    echo "1) wordpress:latest (官方最新版 + Apache)"
    echo "2) wordpress:6.4-apache (官方指定版本 + Apache)"
    echo "3) wordpress:6.4-fpm-alpine (官方轻量版 + PHP-FPM + Nginx)"
    echo "4) bitnami/wordpress:latest (Bitnami优化版)"
    echo "5) 自定义镜像"
    echo "6) 查看当前配置"
    echo "0) 退出"
    echo ""
}

# 更新.env文件
update_env() {
    local image=$1
    if [ -f .env ]; then
        sed -i.bak "s|WP_IMAGE=.*|WP_IMAGE=$image|" .env
    else
        echo "WP_IMAGE=$image" >> .env
    fi
    echo "✅ 已更新镜像配置为: $image"
}

# 切换到FPM配置
switch_to_fpm() {
    echo "🔄 切换到PHP-FPM + Nginx配置..."
    if [ -f docker-compose.yml ]; then
        cp docker-compose.yml docker-compose.yml.backup
    fi
    cp docker-compose.fpm.yml docker-compose.yml
    echo "✅ 已切换到FPM配置"
    echo "💡 使用 Nginx + PHP-FPM，性能更好"
}

# 切换到Bitnami配置
switch_to_bitnami() {
    echo "🔄 切换到Bitnami配置..."
    if [ -f docker-compose.yml ]; then
        cp docker-compose.yml docker-compose.yml.backup
    fi
    cp docker-compose.bitnami.yml docker-compose.yml
    
    # 更新.env文件为Bitnami默认值
    cat > .env << EOF
# WordPress镜像配置 - Bitnami版本
WP_IMAGE=bitnami/wordpress:latest

# Bitnami WordPress配置
WP_ADMIN_USER=admin
WP_ADMIN_PASSWORD=bitnami123
WP_ADMIN_EMAIL=<EMAIL>

# Bitnami数据库配置
DB_NAME=bitnami_wordpress
DB_USER=bn_wordpress
DB_PASSWORD=bitnami123
DB_ROOT_PASSWORD=rootpassword

# WordPress配置
WP_DEBUG=true
WP_DEBUG_LOG=true
WP_DEBUG_DISPLAY=false

# 端口配置
WP_PORT=8080
PHPMYADMIN_PORT=8081
DB_PORT=3306
EOF
    
    echo "✅ 已切换到Bitnami配置"
    echo "💡 Bitnami版本包含更多优化和安全特性"
}

# 恢复默认配置
restore_default() {
    echo "🔄 恢复默认配置..."
    if [ -f docker-compose.yml.backup ]; then
        mv docker-compose.yml.backup docker-compose.yml
        echo "✅ 已恢复默认配置"
    else
        echo "❌ 未找到备份文件"
    fi
}

# 显示镜像信息
show_image_info() {
    echo ""
    echo "📋 WordPress镜像对比："
    echo ""
    echo "1. wordpress:latest"
    echo "   - 官方镜像，Apache服务器"
    echo "   - 最新WordPress版本"
    echo "   - 适合快速开发"
    echo ""
    echo "2. wordpress:6.4-apache"
    echo "   - 官方镜像，指定版本"
    echo "   - 版本稳定，适合生产环境测试"
    echo ""
    echo "3. wordpress:6.4-fpm-alpine"
    echo "   - 基于Alpine Linux，体积小"
    echo "   - PHP-FPM + Nginx，性能更好"
    echo "   - 适合性能要求高的开发"
    echo ""
    echo "4. bitnami/wordpress:latest"
    echo "   - Bitnami优化版本"
    echo "   - 包含更多安全配置"
    echo "   - 预配置开发工具"
    echo "   - 适合企业级开发"
    echo ""
}

# 主循环
while true; do
    show_current_config
    show_menu
    read -p "请选择选项 (0-6): " choice
    
    case $choice in
        1)
            update_env "wordpress:latest"
            restore_default
            ;;
        2)
            update_env "wordpress:6.4-apache"
            restore_default
            ;;
        3)
            update_env "wordpress:6.4-fpm-alpine"
            switch_to_fpm
            ;;
        4)
            switch_to_bitnami
            ;;
        5)
            read -p "请输入自定义镜像名称: " custom_image
            if [ ! -z "$custom_image" ]; then
                update_env "$custom_image"
            else
                echo "❌ 镜像名称不能为空"
            fi
            ;;
        6)
            show_image_info
            ;;
        0)
            echo "👋 再见！"
            exit 0
            ;;
        *)
            echo "❌ 无效选项，请重新选择"
            ;;
    esac
    
    echo ""
    read -p "按回车键继续..."
done
