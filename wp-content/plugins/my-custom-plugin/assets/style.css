/* 自定义插件样式 */
.my-custom-shortcode {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 20px;
    margin: 20px 0;
}

.my-custom-shortcode h3 {
    color: #0073aa;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.my-custom-shortcode p {
    margin: 0;
    line-height: 1.6;
}

/* 管理页面样式 */
.my-custom-plugin-admin {
    max-width: 800px;
}

.my-custom-plugin-admin .form-table th {
    width: 200px;
}

/* 自定义文章类型样式 */
.post-type-custom_item .dashicons-admin-generic:before {
    color: #0073aa;
}
