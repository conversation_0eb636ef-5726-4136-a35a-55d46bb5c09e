/**
 * 自定义插件JavaScript
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        // 插件初始化
        console.log('My Custom Plugin loaded');
        
        // 示例：为短代码添加交互功能
        $('.my-custom-shortcode').on('click', function() {
            $(this).toggleClass('active');
        });
        
        // 示例：AJAX请求
        function sendAjaxRequest(data) {
            $.ajax({
                url: ajaxurl, // WordPress AJAX URL
                type: 'POST',
                data: {
                    action: 'my_custom_plugin_action',
                    nonce: my_custom_plugin.nonce,
                    data: data
                },
                success: function(response) {
                    console.log('AJAX Success:', response);
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                }
            });
        }
        
        // 示例：表单验证
        $('#my-custom-plugin-form').on('submit', function(e) {
            var isValid = true;
            
            // 验证必填字段
            $(this).find('[required]').each(function() {
                if (!$(this).val().trim()) {
                    isValid = false;
                    $(this).addClass('error');
                } else {
                    $(this).removeClass('error');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('请填写所有必填字段');
            }
        });
    });

})(jQuery);
