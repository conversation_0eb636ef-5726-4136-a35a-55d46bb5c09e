<?php
/**
 * Plugin Name: My Custom Plugin
 * Plugin URI: https://example.com
 * Description: 自定义WordPress插件，用于本地开发和测试
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL v2 or later
 * Text Domain: my-custom-plugin
 * Domain Path: /languages
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 定义插件常量
define('MY_CUSTOM_PLUGIN_VERSION', '1.0.0');
define('MY_CUSTOM_PLUGIN_URL', plugin_dir_url(__FILE__));
define('MY_CUSTOM_PLUGIN_PATH', plugin_dir_path(__FILE__));

/**
 * 主插件类
 */
class MyCustomPlugin {
    
    /**
     * 构造函数
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        
        // 激活和停用钩子
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * 初始化插件
     */
    public function init() {
        // 加载文本域
        load_plugin_textdomain('my-custom-plugin', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // 添加短代码
        add_shortcode('my_custom_shortcode', array($this, 'shortcode_handler'));
        
        // 添加自定义文章类型
        $this->register_post_types();
    }
    
    /**
     * 添加管理菜单
     */
    public function add_admin_menu() {
        add_options_page(
            '我的自定义插件',
            '我的自定义插件',
            'manage_options',
            'my-custom-plugin',
            array($this, 'admin_page')
        );
    }
    
    /**
     * 管理页面
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>我的自定义插件设置</h1>
            <form method="post" action="options.php">
                <?php
                settings_fields('my_custom_plugin_settings');
                do_settings_sections('my_custom_plugin_settings');
                ?>
                <table class="form-table">
                    <tr>
                        <th scope="row">示例设置</th>
                        <td>
                            <input type="text" name="my_custom_plugin_option" 
                                   value="<?php echo esc_attr(get_option('my_custom_plugin_option')); ?>" />
                            <p class="description">这是一个示例设置选项</p>
                        </td>
                    </tr>
                </table>
                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }
    
    /**
     * 加载脚本和样式
     */
    public function enqueue_scripts() {
        wp_enqueue_style('my-custom-plugin-style', MY_CUSTOM_PLUGIN_URL . 'assets/style.css', array(), MY_CUSTOM_PLUGIN_VERSION);
        wp_enqueue_script('my-custom-plugin-script', MY_CUSTOM_PLUGIN_URL . 'assets/script.js', array('jquery'), MY_CUSTOM_PLUGIN_VERSION, true);
    }
    
    /**
     * 短代码处理器
     */
    public function shortcode_handler($atts) {
        $atts = shortcode_atts(array(
            'title' => '默认标题',
            'content' => '默认内容'
        ), $atts);
        
        return '<div class="my-custom-shortcode"><h3>' . esc_html($atts['title']) . '</h3><p>' . esc_html($atts['content']) . '</p></div>';
    }
    
    /**
     * 注册自定义文章类型
     */
    public function register_post_types() {
        register_post_type('custom_item', array(
            'labels' => array(
                'name' => '自定义项目',
                'singular_name' => '自定义项目',
                'add_new' => '添加新项目',
                'add_new_item' => '添加新的自定义项目',
                'edit_item' => '编辑自定义项目',
                'new_item' => '新自定义项目',
                'view_item' => '查看自定义项目',
                'search_items' => '搜索自定义项目',
                'not_found' => '未找到自定义项目',
                'not_found_in_trash' => '回收站中未找到自定义项目'
            ),
            'public' => true,
            'has_archive' => true,
            'supports' => array('title', 'editor', 'thumbnail'),
            'menu_icon' => 'dashicons-admin-generic'
        ));
    }
    
    /**
     * 插件激活
     */
    public function activate() {
        // 刷新重写规则
        flush_rewrite_rules();
        
        // 设置默认选项
        add_option('my_custom_plugin_option', '默认值');
    }
    
    /**
     * 插件停用
     */
    public function deactivate() {
        // 刷新重写规则
        flush_rewrite_rules();
    }
}

// 初始化插件
new MyCustomPlugin();
