/**
 * 主题主要JavaScript文件
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        
        // 移动端菜单切换
        $('.menu-toggle').on('click', function() {
            $('.main-navigation ul').slideToggle();
        });

        // 平滑滚动
        $('a[href^="#"]').on('click', function(e) {
            var target = $(this.getAttribute('href'));
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 500);
            }
        });

        // 返回顶部按钮
        var backToTop = $('<button class="back-to-top" title="返回顶部">↑</button>');
        $('body').append(backToTop);

        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                backToTop.fadeIn();
            } else {
                backToTop.fadeOut();
            }
        });

        backToTop.on('click', function() {
            $('html, body').animate({scrollTop: 0}, 500);
        });

        // 图片懒加载（简单实现）
        $('img[data-src]').each(function() {
            var img = $(this);
            var src = img.attr('data-src');
            
            if (isElementInViewport(img[0])) {
                img.attr('src', src).removeAttr('data-src');
            }
        });

        $(window).scroll(function() {
            $('img[data-src]').each(function() {
                var img = $(this);
                var src = img.attr('data-src');
                
                if (isElementInViewport(img[0])) {
                    img.attr('src', src).removeAttr('data-src');
                }
            });
        });

        // 表单验证
        $('form').on('submit', function(e) {
            var form = $(this);
            var isValid = true;

            // 检查必填字段
            form.find('[required]').each(function() {
                var field = $(this);
                if (!field.val().trim()) {
                    field.addClass('error');
                    isValid = false;
                } else {
                    field.removeClass('error');
                }
            });

            // 邮箱验证
            form.find('input[type="email"]').each(function() {
                var email = $(this);
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (email.val() && !emailRegex.test(email.val())) {
                    email.addClass('error');
                    isValid = false;
                } else {
                    email.removeClass('error');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('请检查表单中的错误');
            }
        });

        // 评论表单增强
        $('#commentform').on('submit', function() {
            var comment = $('#comment').val().trim();
            if (comment.length < 10) {
                alert('评论内容至少需要10个字符');
                return false;
            }
        });

        console.log('主题JavaScript已加载');
    });

    // 辅助函数：检查元素是否在视口中
    function isElementInViewport(el) {
        var rect = el.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

})(jQuery);
