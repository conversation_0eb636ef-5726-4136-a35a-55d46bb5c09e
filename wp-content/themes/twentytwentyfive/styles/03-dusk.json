{"$schema": "https://schemas.wp.org/wp/6.7/theme.json", "version": 3, "title": "Dusk", "settings": {"color": {"palette": [{"color": "#E2E2E2", "name": "Base", "slug": "base"}, {"color": "#3B3B3B", "name": "Contrast", "slug": "contrast"}, {"color": "#F5EDFF", "name": "Accent 1", "slug": "accent-1"}, {"color": "#650DD4", "name": "Accent 2", "slug": "accent-2"}, {"color": "#191919", "name": "Accent 3", "slug": "accent-3"}, {"color": "#5F5F5F", "name": "Accent 4", "slug": "accent-4"}, {"color": "#DBDBDB", "name": "Accent 5", "slug": "accent-5"}, {"color": "#3B3B3B33", "name": "Accent 6", "slug": "accent-6"}]}, "custom": {"color": {"accent-2-opacity-20": "#650DD433"}}, "typography": {"fontFamilies": [{"name": "<PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON>", "fontFamily": "<PERSON><PERSON>orn, serif", "fontFace": [{"src": ["file:./assets/fonts/vollkorn/Vollkorn-Italic-VariableFont_wght.woff2"], "fontWeight": "400 900", "fontStyle": "italic", "fontFamily": "<PERSON><PERSON><PERSON>"}, {"src": ["file:./assets/fonts/vollkorn/Vollkorn-VariableFont_wght.woff2"], "fontWeight": "400 900", "fontStyle": "normal", "fontFamily": "<PERSON><PERSON><PERSON>"}]}, {"name": "Fira Code", "slug": "fira-code", "fontFamily": "\"Fira Code\", monospace", "fontFace": [{"src": ["file:./assets/fonts/fira-code/FiraCode-VariableFont_wght.woff2"], "fontWeight": "300 700", "fontStyle": "normal", "fontFamily": "\"Fira Code\""}]}]}}, "styles": {"typography": {"fontFamily": "var:preset|font-family|fira-code", "fontSize": "var:preset|font-size|medium", "letterSpacing": "-0.18px", "lineHeight": "1.5"}, "blocks": {"core/code": {"color": {"text": "var:preset|color|black", "background": "var:preset|color|accent-5"}}, "core/paragraph": {"elements": {"link": {"color": {"text": "var:preset|color|accent-2"}}}}, "core/post-author-name": {"typography": {"fontWeight": "300"}, "color": {"text": "var:preset|color|accent-2"}, "elements": {"link": {"color": {"text": "currentColor"}}}}, "core/post-terms": {"typography": {"fontWeight": "300"}, "color": {"text": "var:preset|color|accent-2"}, "elements": {"link": {"color": {"text": "currentColor"}}}}, "core/post-title": {"typography": {"fontWeight": "400", "letterSpacing": "-0.96px"}, "elements": {"link": {"color": {"text": "var:preset|color|accent-3"}}}}, "core/pullquote": {"color": {"text": "var:preset|color|black"}, "typography": {"fontFamily": "var:preset|font-family|vollkorn", "fontSize": "var:preset|font-size|x-large", "fontWeight": "400"}, "elements": {"cite": {"typography": {"fontFamily": "var:preset|font-family|fira-code", "fontWeight": "300", "letterSpacing": "-0.14px"}, "color": {"text": "var:preset|color|contrast"}}}}, "core/quote": {"color": {"text": "var:preset|color|black"}, "typography": {"fontFamily": "var:preset|font-family|fira-code", "fontWeight": "500", "letterSpacing": "-0.18px"}}, "core/site-title": {"color": {"text": "var:preset|color|accent-3"}, "typography": {"fontFamily": "var:preset|font-family|vollkorn", "fontSize": "var:preset|font-size|x-large"}, "elements": {"link": {"color": {"text": "var:preset|color|accent-3"}}}}}, "elements": {"button": {"typography": {"fontFamily": "var:preset|font-family|fira-code", "fontSize": "var:preset|font-size|medium", "fontWeight": "400", "letterSpacing": "-0.36px"}, "color": {"text": "var:preset|color|base", "background": "var:preset|color|accent-2"}, "border": {"radius": "4px", "color": "transparent"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-2) 85%, transparent)"}}}, "heading": {"color": {"text": "var:preset|color|accent-3"}, "typography": {"fontFamily": "var:preset|font-family|vollkorn"}}, "h1": {"typography": {"fontSize": "48px", "letterSpacing": "-0.96px;"}}, "h2": {"typography": {"fontSize": "38px", "letterSpacing": "-0.96px"}}, "h3": {"typography": {"fontSize": "32px", "letterSpacing": "-0.64px"}}, "h4": {"typography": {"fontSize": "28px", "letterSpacing": "-0.56px"}}, "h5": {"typography": {"fontSize": "24px", "letterSpacing": "-0.48px"}}, "link": {"color": {"text": "var:preset|color|accent-3"}}}, "variations": {"post-terms-1": {"elements": {"link": {"border": {"color": "var:custom|color|accent-2-opacity-20", "radius": "4px", "width": "0.8px", "style": "solid"}}}}, "section-1": {"elements": {"link": {"color": {"text": "var:preset|color|accent-3"}}}}, "section-2": {"color": {"text": "var:preset|color|base"}, "elements": {"button": {"color": {"background": "var:preset|color|accent-3", "text": "var:preset|color|base"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-3) 85%, transparent)"}}}, "link": {"color": {"text": "currentColor"}}, "heading": {"color": {"text": "currentColor"}}}}, "section-3": {"color": {"text": "var:preset|color|accent-2"}, "elements": {"heading": {"color": {"text": "currentColor"}}}}, "section-4": {"color": {"text": "var:preset|color|base"}, "elements": {"button": {"color": {"text": "var:preset|color|base"}, ":hover": {"color": {"text": "var:preset|color|base"}}}, "link": {"color": {"text": "currentColor"}}, "heading": {"color": {"text": "currentColor"}}}}, "section-5": {"elements": {"button": {"color": {"background": "var:preset|color|accent-2", "text": "var:preset|color|base"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-2) 85%, transparent)", "text": "var:preset|color|base"}}}, "heading": {"color": {"text": "var:preset|color|base"}}, "link": {"color": {"text": "var:preset|color|base"}}}}}}}