{"version": 2, "title": "Swiss", "settings": {"color": {"duotone": [{"colors": ["#000000", "#FFFFFF"], "slug": "default-filter", "name": "Default filter"}], "palette": [{"slug": "foreground", "color": "#FFFFFF", "name": "Foreground"}, {"slug": "background", "color": "#1A1A1A", "name": "Background"}, {"slug": "primary", "color": "#FF7179", "name": "Primary"}, {"slug": "secondary", "color": "#F4F4F2", "name": "Secondary"}, {"slug": "tertiary", "color": "#0000000", "name": "Tertiary"}]}, "custom": {"spacing": {"small": "max(1.25rem, 5vw)", "medium": "clamp(1.75rem, 6vw, calc(3 * var(--wp--style--block-gap)))", "large": "clamp(3.5rem, 8vw, 6rem)", "outer": "var(--wp--custom--spacing--small, 1.25rem)"}, "typography": {"font-size": {"colossal": "clamp(3.5rem, 8vw, 5rem)"}}}, "typography": {"fontFamilies": [{"fontFamily": "\"Inter\", sans-serif", "name": "Inter", "slug": "inter", "fontFace": [{"fontFamily": "Inter", "fontWeight": "200 900", "fontStretch": "normal", "src": ["file:./assets/fonts/inter/Inter.ttf"]}]}]}}, "styles": {"blocks": {"core/cover": {"filter": {"duotone": "var(--wp--preset--duotone--default-filter)"}}, "core/image": {"filter": {"duotone": "var(--wp--preset--duotone--default-filter)"}}, "core/post-author": {"filter": {"duotone": "var(--wp--preset--duotone--default-filter)"}}, "core/post-featured-image": {"filter": {"duotone": "var(--wp--preset--duotone--default-filter)"}}, "core/post-title": {"typography": {"fontFamily": "var(--wp--preset--font-family--inter)", "fontWeight": "700"}}, "core/query-title": {"typography": {"fontFamily": "var(--wp--preset--font-family--inter)"}}, "core/site-logo": {"filter": {"duotone": "var(--wp--preset--duotone--default-filter)"}}, "core/site-title": {"typography": {"fontWeight": "700", "fontStyle": "normal"}}}, "elements": {"h1": {"typography": {"fontFamily": "var(--wp--preset--font-family--inter)", "fontWeight": "700"}}, "h2": {"typography": {"fontFamily": "var(--wp--preset--font-family--inter)", "fontWeight": "700"}}, "h3": {"typography": {"fontFamily": "var(--wp--preset--font-family--inter)", "fontWeight": "700"}}, "h4": {"typography": {"fontFamily": "var(--wp--preset--font-family--inter)", "fontWeight": "700"}}, "h5": {"typography": {"fontFamily": "var(--wp--preset--font-family--inter)", "fontWeight": "700"}}, "h6": {"typography": {"fontFamily": "var(--wp--preset--font-family--inter)", "fontWeight": "700"}}, "link": {"color": {"text": "var(--wp--preset--color--primary)"}}}, "typography": {"fontFamily": "var(--wp--preset--font-family--inter)"}}}