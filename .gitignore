# WordPress核心文件（如果不需要版本控制）
# wp-admin/
# wp-includes/
# wp-*.php
# index.php
# xmlrpc.php
# .htaccess

# WordPress配置文件
wp-config.php
wp-config-local.php

# 上传文件
wp-content/uploads/
wp-content/upgrade/
wp-content/backup-db/
wp-content/advanced-cache.php
wp-content/wp-cache-config.php
wp-content/cache/
wp-content/backups/

# 插件和主题（如果使用包管理器）
# wp-content/plugins/
# wp-content/themes/

# 日志文件
*.log
wp-content/debug.log
error_log

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 编辑器文件
*.swp
*.swo
*~
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境变量文件（如果包含敏感信息）
# .env

# Docker
.docker/

# 临时文件
*.tmp
*.temp

# 数据库备份
*.sql
*.sql.gz

# 压缩文件
*.zip
*.tar.gz
*.rar

# PHP
vendor/
composer.phar
composer.lock
