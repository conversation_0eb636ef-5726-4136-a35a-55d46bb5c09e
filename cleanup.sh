#!/bin/bash

# WordPress Docker开发环境清理脚本

echo "🧹 清理WordPress Docker开发环境..."

# 确认操作
read -p "⚠️  这将删除所有容器、镜像和数据库数据。确定要继续吗？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 操作已取消"
    exit 1
fi

# 停止并删除容器
echo "🛑 停止并删除容器..."
docker-compose down -v

# 删除相关镜像
echo "🗑️  删除相关镜像..."
docker rmi $(docker images | grep -E "(wordpress|mysql|phpmyadmin)" | awk '{print $3}') 2>/dev/null || true

# 清理未使用的卷
echo "🧽 清理未使用的卷..."
docker volume prune -f

# 清理未使用的网络
echo "🌐 清理未使用的网络..."
docker network prune -f

echo "✅ 清理完成！"
echo ""
echo "💡 提示："
echo "   重新开始: ./start.sh"
echo ""
