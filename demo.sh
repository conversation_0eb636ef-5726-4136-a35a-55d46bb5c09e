#!/bin/bash

# WordPress Docker环境演示脚本

echo "🎯 WordPress Docker开发环境演示"
echo "================================"

# 检查Docker是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo "❌ Docker未运行，请先启动Docker"
        exit 1
    fi
    echo "✅ Docker运行正常"
}

# 显示当前环境信息
show_environment_info() {
    echo ""
    echo "📋 当前环境信息："
    echo "--------------------------------"
    
    if [ -f .env ]; then
        echo "WordPress镜像: $(grep WP_IMAGE .env | cut -d'=' -f2)"
        echo "数据库名: $(grep DB_NAME .env | cut -d'=' -f2)"
        echo "数据库用户: $(grep DB_USER .env | cut -d'=' -f2)"
    else
        echo "WordPress镜像: wordpress:latest (默认)"
        echo "数据库名: wordpress"
        echo "数据库用户: wordpress"
    fi
    
    echo "WordPress端口: 8080"
    echo "phpMyAdmin端口: 8081"
    echo "数据库端口: 3306"
}

# 显示可用的镜像选项
show_image_options() {
    echo ""
    echo "🐳 可用的WordPress镜像："
    echo "--------------------------------"
    echo "1. wordpress:latest - 官方最新版 (Apache)"
    echo "2. wordpress:6.4-apache - 官方稳定版 (Apache)"
    echo "3. wordpress:6.4-fpm-alpine - 轻量版 (Nginx + PHP-FPM)"
    echo "4. bitnami/wordpress:latest - 企业优化版"
    echo ""
    echo "💡 使用 ./switch-image.sh 来切换镜像"
}

# 显示开发工具
show_dev_tools() {
    echo ""
    echo "🛠️ 开发工具："
    echo "--------------------------------"
    echo "• WP-CLI: ./wp-cli-commands.sh"
    echo "• 镜像切换: ./switch-image.sh"
    echo "• 环境启动: ./start.sh"
    echo "• 环境停止: ./stop.sh"
    echo "• 环境清理: ./cleanup.sh"
}

# 显示示例主题和插件
show_examples() {
    echo ""
    echo "📁 示例代码："
    echo "--------------------------------"
    echo "主题: wp-content/themes/my-custom-theme/"
    echo "  ├── style.css (主样式文件)"
    echo "  ├── index.php (主模板)"
    echo "  ├── header.php (头部模板)"
    echo "  ├── footer.php (底部模板)"
    echo "  ├── functions.php (功能文件)"
    echo "  └── js/main.js (JavaScript文件)"
    echo ""
    echo "插件: wp-content/plugins/my-custom-plugin/"
    echo "  ├── my-custom-plugin.php (主插件文件)"
    echo "  └── assets/ (样式和脚本)"
}

# 显示快速开始指南
show_quick_start() {
    echo ""
    echo "🚀 快速开始："
    echo "--------------------------------"
    echo "1. 选择WordPress镜像:"
    echo "   ./switch-image.sh"
    echo ""
    echo "2. 启动开发环境:"
    echo "   ./start.sh"
    echo ""
    echo "3. 访问WordPress:"
    echo "   前台: http://localhost:8080"
    echo "   后台: http://localhost:8080/wp-admin"
    echo "   数据库: http://localhost:8081"
    echo ""
    echo "4. 使用WP-CLI:"
    echo "   ./wp-cli-commands.sh"
    echo ""
    echo "5. 开发主题和插件:"
    echo "   编辑 wp-content/themes/ 和 wp-content/plugins/ 目录"
}

# 显示性能对比
show_performance_comparison() {
    echo ""
    echo "⚡ 性能对比："
    echo "--------------------------------"
    echo "镜像大小 (近似):"
    echo "• wordpress:latest      ~600MB"
    echo "• wordpress:fpm-alpine  ~200MB"
    echo "• bitnami/wordpress     ~800MB"
    echo ""
    echo "启动时间 (近似):"
    echo "• Apache版本           ~30秒"
    echo "• FPM + Nginx版本      ~25秒"
    echo "• Bitnami版本          ~45秒"
    echo ""
    echo "内存使用 (近似):"
    echo "• Apache版本           ~150MB"
    echo "• FPM + Nginx版本      ~120MB"
    echo "• Bitnami版本          ~200MB"
}

# 主函数
main() {
    check_docker
    show_environment_info
    show_image_options
    show_dev_tools
    show_examples
    show_quick_start
    show_performance_comparison
    
    echo ""
    echo "🎉 演示完成！开始你的WordPress开发之旅吧！"
    echo ""
    echo "💡 提示："
    echo "• 查看完整文档: cat README.md"
    echo "• 获取帮助: 查看各个脚本的 --help 选项"
    echo ""
}

# 运行主函数
main
