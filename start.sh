#!/bin/bash

# WordPress Docker开发环境启动脚本

echo "🚀 启动WordPress Docker开发环境..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p wp-content/themes wp-content/plugins wp-content/uploads
chmod 755 wp-content/uploads

# 启动Docker容器
echo "🐳 启动Docker容器..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

echo ""
echo "✅ WordPress开发环境已启动！"
echo ""
echo "🌐 访问地址："
echo "   WordPress前台: http://localhost:8080"
echo "   WordPress后台: http://localhost:8080/wp-admin"
echo "   phpMyAdmin:   http://localhost:8081"
echo ""
echo "🔑 数据库信息："
echo "   数据库名: wordpress"
echo "   用户名:   wordpress"
echo "   密码:     wordpress"
echo ""
echo "📝 常用命令："
echo "   停止环境: ./stop.sh"
echo "   查看日志: docker-compose logs -f"
echo "   进入容器: docker-compose exec wordpress bash"
echo "   WP-CLI:   docker-compose run --rm wpcli wp --info"
echo ""
