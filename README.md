# WordPress Docker 开发环境

这是一个完整的WordPress Docker开发环境，专为主题和插件开发而设计。

## 🚀 快速开始

### 前置要求
- Docker Desktop
- Docker Compose

### 启动环境

```bash
# 给脚本执行权限
chmod +x *.sh

# 启动开发环境
./start.sh
```

### 访问地址
- **WordPress前台**: http://localhost:8080
- **WordPress后台**: http://localhost:8080/wp-admin
- **phpMyAdmin**: http://localhost:8081

### 默认账户信息
- **数据库名**: wordpress
- **数据库用户**: wordpress
- **数据库密码**: wordpress
- **Root密码**: rootpassword

## 📁 项目结构

```
wp-com-site/
├── docker-compose.yml      # Docker编排配置
├── .env                    # 环境变量
├── config/                 # 配置文件
│   ├── php.ini            # PHP配置
│   └── mysql.cnf          # MySQL配置
├── wp-content/            # WordPress内容目录
│   ├── themes/            # 自定义主题
│   │   └── my-custom-theme/
│   ├── plugins/           # 自定义插件
│   │   └── my-custom-plugin/
│   └── uploads/           # 上传文件
├── start.sh               # 启动脚本
├── stop.sh                # 停止脚本
└── cleanup.sh             # 清理脚本
```

## 🛠️ 开发工具

### WP-CLI 使用
```bash
# 查看WordPress信息
docker-compose run --rm wpcli wp --info

# 安装插件
docker-compose run --rm wpcli wp plugin install akismet

# 激活主题
docker-compose run --rm wpcli wp theme activate my-custom-theme

# 创建用户
docker-compose run --rm wpcli wp user <NAME_EMAIL> --role=administrator
```

### 数据库操作
```bash
# 导出数据库
docker-compose exec db mysqldump -u wordpress -pwordpress wordpress > backup.sql

# 导入数据库
docker-compose exec -T db mysql -u wordpress -pwordpress wordpress < backup.sql
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f wordpress
docker-compose logs -f db
```

## 🎨 主题开发

示例主题位于 `wp-content/themes/my-custom-theme/`，包含：
- `style.css` - 主样式文件
- `index.php` - 主模板文件
- `header.php` - 头部模板
- `footer.php` - 底部模板
- `functions.php` - 主题功能文件

### 主题特性
- 响应式设计
- 自定义菜单支持
- 文章缩略图支持
- 调试模式显示当前模板

## 🔌 插件开发

示例插件位于 `wp-content/plugins/my-custom-plugin/`，包含：
- 自定义文章类型
- 短代码功能
- 管理页面
- 前端样式和脚本

### 插件功能
- 短代码: `[my_custom_shortcode title="标题" content="内容"]`
- 自定义文章类型: custom_item
- 管理设置页面

## 🔧 配置说明

### PHP 配置 (config/php.ini)
- 上传文件大小限制: 64MB
- 内存限制: 256MB
- 错误显示: 开启
- Xdebug: 配置完成

### MySQL 配置 (config/mysql.cnf)
- 字符集: utf8mb4
- 慢查询日志: 开启
- 性能优化配置

## 🐛 调试

### 开启WordPress调试
调试模式已在 `.env` 文件中配置：
```
WP_DEBUG=true
WP_DEBUG_LOG=true
WP_DEBUG_DISPLAY=false
```

### 查看错误日志
```bash
# WordPress错误日志
docker-compose exec wordpress tail -f /var/www/html/wp-content/debug.log

# PHP错误日志
docker-compose exec wordpress tail -f /var/log/php_errors.log
```

## 📝 常用命令

```bash
# 启动环境
./start.sh

# 停止环境
./stop.sh

# 完全清理环境
./cleanup.sh

# 进入WordPress容器
docker-compose exec wordpress bash

# 进入数据库容器
docker-compose exec db mysql -u wordpress -pwordpress wordpress

# 重启特定服务
docker-compose restart wordpress
```

## 🔒 安全注意事项

⚠️ **重要**: 此环境仅用于本地开发，不要在生产环境中使用！

- 默认密码简单，仅适用于开发
- 调试模式开启，会暴露敏感信息
- 数据库端口对外开放，便于调试

## 🤝 贡献

欢迎提交问题和改进建议！

## 📄 许可证

MIT License
