# WordPress Docker 开发环境

这是一个完整的WordPress Docker开发环境，专为主题和插件开发而设计。

## 🚀 快速开始

### 前置要求
- Docker Desktop
- Docker Compose

### 启动环境

```bash
# 给脚本执行权限
chmod +x *.sh

# 选择WordPress镜像（可选）
./switch-image.sh

# 启动开发环境
./start.sh
```

### 访问地址
- **WordPress前台**: http://localhost:8080
- **WordPress后台**: http://localhost:8080/wp-admin
- **phpMyAdmin**: http://localhost:8081 (如果启用)

### 默认账户信息
- **数据库名**: wordpress
- **数据库用户**: wordpress
- **数据库密码**: wordpress
- **Root密码**: rootpassword

## 📁 项目结构

```
wp-com-site/
├── docker-compose.yml      # Docker编排配置
├── .env                    # 环境变量
├── config/                 # 配置文件
│   ├── php.ini            # PHP配置
│   └── mysql.cnf          # MySQL配置
├── wp-content/            # WordPress内容目录
│   ├── themes/            # 自定义主题
│   │   └── my-custom-theme/
│   ├── plugins/           # 自定义插件
│   │   └── my-custom-plugin/
│   └── uploads/           # 上传文件
├── start.sh               # 启动脚本
├── stop.sh                # 停止脚本
├── cleanup.sh             # 清理脚本
├── switch-image.sh        # 镜像切换脚本
├── wp-cli-commands.sh     # WP-CLI常用命令
├── docker-compose.fpm.yml # PHP-FPM版本配置
└── docker-compose.bitnami.yml # Bitnami版本配置
```

## 🐳 WordPress镜像选择

本环境支持多种WordPress镜像，你可以根据需求选择：

### 可用镜像

1. **wordpress:latest** (默认)
   - 官方最新版本 + Apache
   - 快速启动，适合日常开发
   - 体积适中，功能完整

2. **wordpress:6.4-apache**
   - 官方指定版本 + Apache
   - 版本稳定，适合生产环境测试
   - 与生产环境版本保持一致

3. **wordpress:6.4-fpm-alpine**
   - 基于Alpine Linux，体积最小
   - PHP-FPM + Nginx，性能最佳
   - 适合性能要求高的开发

4. **bitnami/wordpress:latest**
   - Bitnami优化版本
   - 包含更多安全配置和开发工具
   - 预配置优化，适合企业级开发

### 切换镜像

```bash
# 运行镜像切换工具
./switch-image.sh

# 或者直接编辑.env文件
echo "WP_IMAGE=bitnami/wordpress:latest" > .env
```

### 镜像对比

| 特性 | Official | Official FPM | Bitnami |
|------|----------|--------------|---------|
| 服务器 | Apache | Nginx + PHP-FPM | Apache |
| 体积 | 中等 | 最小 | 较大 |
| 性能 | 良好 | 最佳 | 优秀 |
| 安全性 | 标准 | 标准 | 增强 |
| 开发工具 | 基础 | 基础 | 丰富 |
| 配置复杂度 | 简单 | 中等 | 简单 |

## 🛠️ 开发工具

### WP-CLI 使用
```bash
# 查看WordPress信息
docker-compose run --rm wpcli wp --info

# 安装插件
docker-compose run --rm wpcli wp plugin install akismet

# 激活主题
docker-compose run --rm wpcli wp theme activate my-custom-theme

# 创建用户
docker-compose run --rm wpcli wp user <NAME_EMAIL> --role=administrator
```

### 数据库操作
```bash
# 导出数据库
docker-compose exec db mysqldump -u wordpress -pwordpress wordpress > backup.sql

# 导入数据库
docker-compose exec -T db mysql -u wordpress -pwordpress wordpress < backup.sql
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f wordpress
docker-compose logs -f db
```

## 🎨 主题开发

示例主题位于 `wp-content/themes/my-custom-theme/`，包含：
- `style.css` - 主样式文件
- `index.php` - 主模板文件
- `header.php` - 头部模板
- `footer.php` - 底部模板
- `functions.php` - 主题功能文件

### 主题特性
- 响应式设计
- 自定义菜单支持
- 文章缩略图支持
- 调试模式显示当前模板

## 🔌 插件开发

示例插件位于 `wp-content/plugins/my-custom-plugin/`，包含：
- 自定义文章类型
- 短代码功能
- 管理页面
- 前端样式和脚本

### 插件功能
- 短代码: `[my_custom_shortcode title="标题" content="内容"]`
- 自定义文章类型: custom_item
- 管理设置页面

## 🔧 配置说明

### PHP 配置 (config/php.ini)
- 上传文件大小限制: 64MB
- 内存限制: 256MB
- 错误显示: 开启
- Xdebug: 配置完成

### MySQL 配置 (config/mysql.cnf)
- 字符集: utf8mb4
- 慢查询日志: 开启
- 性能优化配置

## 🐛 调试

### 开启WordPress调试
调试模式已在 `.env` 文件中配置：
```
WP_DEBUG=true
WP_DEBUG_LOG=true
WP_DEBUG_DISPLAY=false
```

### 查看错误日志
```bash
# WordPress错误日志
docker-compose exec wordpress tail -f /var/www/html/wp-content/debug.log

# PHP错误日志
docker-compose exec wordpress tail -f /var/log/php_errors.log
```

## 📝 常用命令

```bash
# 镜像管理
./switch-image.sh          # 切换WordPress镜像
./wp-cli-commands.sh       # WP-CLI常用命令菜单

# 环境管理
./start.sh                 # 启动环境
./stop.sh                  # 停止环境
./cleanup.sh               # 完全清理环境

# 容器操作
docker-compose exec wordpress bash                    # 进入WordPress容器
docker-compose exec db mysql -u wordpress -pwordpress wordpress  # 进入数据库
docker-compose restart wordpress                      # 重启WordPress服务

# 日志查看
docker-compose logs -f wordpress                      # 查看WordPress日志
docker-compose logs -f db                            # 查看数据库日志
```

## 🔒 安全注意事项

⚠️ **重要**: 此环境仅用于本地开发，不要在生产环境中使用！

- 默认密码简单，仅适用于开发
- 调试模式开启，会暴露敏感信息
- 数据库端口对外开放，便于调试

## 🤝 贡献

欢迎提交问题和改进建议！

## 📄 许可证

MIT License
