services:
  # WordPress服务
  wordpress:
    image: wordpress:latest
    container_name: wp-dev
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      WORDPRESS_DB_HOST: db
      WORDPRESS_DB_USER: ${DB_USER:-wordpress}
      WORDPRESS_DB_PASSWORD: ${DB_PASSWORD:-wordpress}
      WORDPRESS_DB_NAME: ${DB_NAME:-wordpress}
      WORDPRESS_DEBUG: 1
      WORDPRESS_CONFIG_EXTRA: |
        define('WP_DEBUG', true);
        define('WP_DEBUG_LOG', true);
        define('WP_DEBUG_DISPLAY', false);
        define('SCRIPT_DEBUG', true);
        define('SAVEQUERIES', true);
    volumes:
      # 挂载自定义主题和插件
      - ./wp-content/themes:/var/www/html/wp-content/themes
      - ./wp-content/plugins:/var/www/html/wp-content/plugins
      - ./wp-content/uploads:/var/www/html/wp-content/uploads
      # 挂载PHP配置
      - ./config/php.ini:/usr/local/etc/php/conf.d/custom.ini
      # 挂载WordPress配置（可选）
      - ./config/wp-config-extra.php:/var/www/html/wp-config-extra.php
    depends_on:
      - db
    networks:
      - wp-network

  # MySQL数据库服务
  db:
    image: mysql:8.0
    container_name: wp-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_NAME:-wordpress}
      MYSQL_USER: ${DB_USER:-wordpress}
      MYSQL_PASSWORD: ${DB_PASSWORD:-wordpress}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-rootpassword}
    volumes:
      - db_data:/var/lib/mysql
      - ./config/mysql.cnf:/etc/mysql/conf.d/custom.cnf
    ports:
      - "3306:3306"
    networks:
      - wp-network

  # phpMyAdmin服务（数据库管理）
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: wp-phpmyadmin
    restart: unless-stopped
    ports:
      - "8081:80"
    environment:
      PMA_HOST: db
      PMA_USER: ${DB_USER:-wordpress}
      PMA_PASSWORD: ${DB_PASSWORD:-wordpress}
    depends_on:
      - db
    networks:
      - wp-network

  # WP-CLI服务（WordPress命令行工具）
  wpcli:
    image: wordpress:cli
    container_name: wp-cli
    volumes:
      - ./wp-content/themes:/var/www/html/wp-content/themes
      - ./wp-content/plugins:/var/www/html/wp-content/plugins
      - ./wp-content/uploads:/var/www/html/wp-content/uploads
    environment:
      WORDPRESS_DB_HOST: db
      WORDPRESS_DB_USER: ${DB_USER:-wordpress}
      WORDPRESS_DB_PASSWORD: ${DB_PASSWORD:-wordpress}
      WORDPRESS_DB_NAME: ${DB_NAME:-wordpress}
    depends_on:
      - db
      - wordpress
    networks:
      - wp-network

volumes:
  db_data:

networks:
  wp-network:
    driver: bridge
