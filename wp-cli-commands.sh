#!/bin/bash

# WordPress WP-CLI 常用命令脚本

echo "🔧 WordPress WP-CLI 常用命令"
echo "================================"

# 显示菜单
show_menu() {
    echo ""
    echo "请选择操作："
    echo "1)  查看WordPress信息"
    echo "2)  创建管理员用户"
    echo "3)  激活自定义主题"
    echo "4)  激活自定义插件"
    echo "5)  生成测试文章"
    echo "6)  列出所有插件"
    echo "7)  列出所有主题"
    echo "8)  数据库导出"
    echo "9)  数据库导入"
    echo "10) 清除缓存"
    echo "11) 搜索替换URL"
    echo "12) 安装常用插件"
    echo "13) 查看用户列表"
    echo "14) 重新生成缩略图"
    echo "15) 查看站点配置"
    echo "0)  退出"
    echo ""
}

# WP-CLI命令前缀
WP_CLI="docker-compose run --rm wpcli wp"

while true; do
    show_menu
    read -p "请输入选项 (0-15): " choice
    
    case $choice in
        1)
            echo "📊 查看WordPress信息..."
            $WP_CLI --info
            ;;
        2)
            read -p "输入管理员用户名: " username
            read -p "输入管理员邮箱: " email
            read -s -p "输入管理员密码: " password
            echo ""
            echo "👤 创建管理员用户..."
            $WP_CLI user create $username $email --role=administrator --user_pass=$password
            ;;
        3)
            echo "🎨 激活自定义主题..."
            $WP_CLI theme activate my-custom-theme
            ;;
        4)
            echo "🔌 激活自定义插件..."
            $WP_CLI plugin activate my-custom-plugin
            ;;
        5)
            read -p "输入要生成的文章数量 (默认10): " count
            count=${count:-10}
            echo "📝 生成 $count 篇测试文章..."
            $WP_CLI post generate --count=$count
            ;;
        6)
            echo "🔌 插件列表..."
            $WP_CLI plugin list
            ;;
        7)
            echo "🎨 主题列表..."
            $WP_CLI theme list
            ;;
        8)
            filename="backup-$(date +%Y%m%d-%H%M%S).sql"
            echo "💾 导出数据库到 $filename..."
            $WP_CLI db export $filename
            ;;
        9)
            read -p "输入SQL文件路径: " sqlfile
            if [ -f "$sqlfile" ]; then
                echo "📥 导入数据库..."
                $WP_CLI db import $sqlfile
            else
                echo "❌ 文件不存在: $sqlfile"
            fi
            ;;
        10)
            echo "🧹 清除缓存..."
            $WP_CLI cache flush
            $WP_CLI rewrite flush
            ;;
        11)
            read -p "输入旧URL: " old_url
            read -p "输入新URL: " new_url
            echo "🔄 搜索替换URL..."
            $WP_CLI search-replace "$old_url" "$new_url"
            ;;
        12)
            echo "📦 安装常用插件..."
            $WP_CLI plugin install akismet contact-form-7 yoast-seo --activate
            ;;
        13)
            echo "👥 用户列表..."
            $WP_CLI user list
            ;;
        14)
            echo "🖼️  重新生成缩略图..."
            $WP_CLI media regenerate --yes
            ;;
        15)
            echo "⚙️  站点配置..."
            echo "站点URL: $($WP_CLI option get siteurl)"
            echo "站点名称: $($WP_CLI option get blogname)"
            echo "管理员邮箱: $($WP_CLI option get admin_email)"
            echo "WordPress版本: $($WP_CLI core version)"
            ;;
        0)
            echo "👋 再见！"
            exit 0
            ;;
        *)
            echo "❌ 无效选项，请重新选择"
            ;;
    esac
    
    echo ""
    read -p "按回车键继续..."
done
