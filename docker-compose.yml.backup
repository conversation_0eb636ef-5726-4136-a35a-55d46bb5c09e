version: '3.8'

services:
  # Nginx Web服务器
  nginx:
    image: nginx:alpine
    container_name: wp-nginx
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./config/nginx.conf:/etc/nginx/conf.d/default.conf
      - ./wp-content/themes:/var/www/html/wp-content/themes
      - ./wp-content/plugins:/var/www/html/wp-content/plugins
      - ./wp-content/uploads:/var/www/html/wp-content/uploads
      - wordpress_data:/var/www/html
    depends_on:
      - wordpress
    networks:
      - wp-network

  # WordPress PHP-FPM服务
  wordpress:
    image: ${WP_IMAGE:-wordpress:6.4-fpm-alpine}
    container_name: wp-fpm
    restart: unless-stopped
    environment:
      WORDPRESS_DB_HOST: db
      WORDPRESS_DB_USER: ${DB_USER:-wordpress}
      WORDPRESS_DB_PASSWORD: ${DB_PASSWORD:-wordpress}
      WORDPRESS_DB_NAME: ${DB_NAME:-wordpress}
      WORDPRESS_DEBUG: 1
      WORDPRESS_CONFIG_EXTRA: |
        define('WP_DEBUG', true);
        define('WP_DEBUG_LOG', true);
        define('WP_DEBUG_DISPLAY', false);
        define('SCRIPT_DEBUG', true);
        define('SAVEQUERIES', true);
    volumes:
      - wordpress_data:/var/www/html
      - ./wp-content/themes:/var/www/html/wp-content/themes
      - ./wp-content/plugins:/var/www/html/wp-content/plugins
      - ./wp-content/uploads:/var/www/html/wp-content/uploads
      - ./config/php.ini:/usr/local/etc/php/conf.d/custom.ini
    depends_on:
      - db
    networks:
      - wp-network

  # MySQL数据库服务
  db:
    image: mysql:8.0
    container_name: wp-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_NAME:-wordpress}
      MYSQL_USER: ${DB_USER:-wordpress}
      MYSQL_PASSWORD: ${DB_PASSWORD:-wordpress}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-rootpassword}
    volumes:
      - db_data:/var/lib/mysql
      - ./config/mysql.cnf:/etc/mysql/conf.d/custom.cnf
    ports:
      - "3306:3306"
    networks:
      - wp-network



  # WP-CLI服务
  wpcli:
    image: wordpress:cli
    container_name: wp-cli
    volumes:
      - wordpress_data:/var/www/html
      - ./wp-content/themes:/var/www/html/wp-content/themes
      - ./wp-content/plugins:/var/www/html/wp-content/plugins
      - ./wp-content/uploads:/var/www/html/wp-content/uploads
    environment:
      WORDPRESS_DB_HOST: db
      WORDPRESS_DB_USER: ${DB_USER:-wordpress}
      WORDPRESS_DB_PASSWORD: ${DB_PASSWORD:-wordpress}
      WORDPRESS_DB_NAME: ${DB_NAME:-wordpress}
    depends_on:
      - db
      - wordpress
    networks:
      - wp-network

volumes:
  db_data:
  wordpress_data:

networks:
  wp-network:
    driver: bridge
