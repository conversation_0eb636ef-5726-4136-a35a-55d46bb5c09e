version: '3.8'

services:
  # WordPress Bitnami服务
  wordpress:
    image: bitnami/wordpress:latest
    container_name: wp-bitnami
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "8443:8443"
    environment:
      # 数据库配置
      WORDPRESS_DATABASE_HOST: mariadb
      WORDPRESS_DATABASE_PORT_NUMBER: 3306
      WORDPRESS_DATABASE_USER: ${DB_USER:-bn_wordpress}
      WORDPRESS_DATABASE_PASSWORD: ${DB_PASSWORD:-bitnami123}
      WORDPRESS_DATABASE_NAME: ${DB_NAME:-bitnami_wordpress}
      
      # WordPress配置
      WORDPRESS_USERNAME: ${WP_ADMIN_USER:-admin}
      WORDPRESS_PASSWORD: ${WP_ADMIN_PASSWORD:-bitnami123}
      WORDPRESS_EMAIL: ${WP_ADMIN_EMAIL:-<EMAIL>}
      WORDPRESS_FIRST_NAME: Admin
      WORDPRESS_LAST_NAME: User
      WORDPRESS_BLOG_NAME: "WordPress开发站点"
      
      # 调试配置
      WORDPRESS_DEBUG: "yes"
      WORDPRESS_ENABLE_HTTPS: "no"
      WORDPRESS_SKIP_BOOTSTRAP: "no"
      
      # 性能配置
      PHP_MEMORY_LIMIT: 256M
      PHP_MAX_EXECUTION_TIME: 300
      PHP_UPLOAD_MAX_FILESIZE: 64M
      PHP_POST_MAX_SIZE: 64M
    volumes:
      - wordpress_data:/bitnami/wordpress
      - ./wp-content/themes:/bitnami/wordpress/wp-content/themes
      - ./wp-content/plugins:/bitnami/wordpress/wp-content/plugins
      - ./wp-content/uploads:/bitnami/wordpress/wp-content/uploads
    depends_on:
      - mariadb
    networks:
      - wp-network

  # MariaDB数据库服务
  mariadb:
    image: bitnami/mariadb:latest
    container_name: wp-mariadb
    restart: unless-stopped
    environment:
      MARIADB_USER: ${DB_USER:-bn_wordpress}
      MARIADB_PASSWORD: ${DB_PASSWORD:-bitnami123}
      MARIADB_DATABASE: ${DB_NAME:-bitnami_wordpress}
      MARIADB_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-rootpassword}
      MARIADB_CHARACTER_SET: utf8mb4
      MARIADB_COLLATE: utf8mb4_unicode_ci
    volumes:
      - mariadb_data:/bitnami/mariadb
    ports:
      - "3306:3306"
    networks:
      - wp-network

  # phpMyAdmin服务
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: wp-phpmyadmin-bitnami
    restart: unless-stopped
    ports:
      - "8081:80"
    environment:
      PMA_HOST: mariadb
      PMA_USER: ${DB_USER:-bn_wordpress}
      PMA_PASSWORD: ${DB_PASSWORD:-bitnami123}
    depends_on:
      - mariadb
    networks:
      - wp-network

  # WP-CLI服务
  wpcli:
    image: bitnami/wordpress:latest
    container_name: wp-cli-bitnami
    entrypoint: wp
    command: --info
    volumes:
      - wordpress_data:/bitnami/wordpress
      - ./wp-content/themes:/bitnami/wordpress/wp-content/themes
      - ./wp-content/plugins:/bitnami/wordpress/wp-content/plugins
      - ./wp-content/uploads:/bitnami/wordpress/wp-content/uploads
    environment:
      WORDPRESS_DATABASE_HOST: mariadb
      WORDPRESS_DATABASE_USER: ${DB_USER:-bn_wordpress}
      WORDPRESS_DATABASE_PASSWORD: ${DB_PASSWORD:-bitnami123}
      WORDPRESS_DATABASE_NAME: ${DB_NAME:-bitnami_wordpress}
    depends_on:
      - mariadb
      - wordpress
    networks:
      - wp-network

volumes:
  wordpress_data:
  mariadb_data:

networks:
  wp-network:
    driver: bridge
